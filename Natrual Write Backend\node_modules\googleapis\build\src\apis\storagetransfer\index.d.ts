/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { storagetransfer_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof storagetransfer_v1.Storagetransfer;
};
export declare function storagetransfer(version: 'v1'): storagetransfer_v1.Storagetransfer;
export declare function storagetransfer(options: storagetransfer_v1.Options): storagetransfer_v1.Storagetransfer;
declare const auth: AuthPlus;
export { auth };
export { storagetransfer_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
