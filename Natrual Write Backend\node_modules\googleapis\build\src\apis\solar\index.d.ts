/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { solar_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof solar_v1.Solar;
};
export declare function solar(version: 'v1'): solar_v1.Solar;
export declare function solar(options: solar_v1.Options): solar_v1.Solar;
declare const auth: AuthPlus;
export { auth };
export { solar_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
