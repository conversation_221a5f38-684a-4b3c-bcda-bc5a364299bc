const jwt = require('jsonwebtoken');
const User = require('../models/User');

const signToken = (id) => {
    return jwt.sign({ id }, process.env.JWT_SECRET || 'your-temporary-secret', {
        expiresIn: process.env.JWT_EXPIRES_IN || '90d'
    });
};

const createSendToken = (user, statusCode, res) => {
    const token = signToken(user._id);

    // Remove password from output
    user.password = undefined;

    res.status(statusCode).json({
        status: 'success',
        token,
        data: {
            user
        }
    });
};

const handleDuplicateFieldsDB = (err) => {
    const field = Object.keys(err.keyValue)[0];
    const value = err.keyValue[field];

    let message = 'Duplicate field value. Please use another value!';
    if (field === 'email') {
        message = `An account with email '${value}' already exists. Please use a different email or try logging in.`;
    }

    return {
        status: 'fail',
        message,
        field
    };
};

const handleValidationErrorDB = (err) => {
    const errors = Object.values(err.errors).map(el => el.message);
    const message = `Invalid input data. ${errors.join('. ')}`;

    return {
        status: 'fail',
        message,
        errors
    };
};

exports.signup = async (req, res) => {
    try {
        const newUser = await User.create({
            name: req.body.name,
            email: req.body.email,
            password: req.body.password
        });

        createSendToken(newUser, 201, res);
    } catch (error) {
        let errorResponse;

        if (error.code === 11000) {
            // Duplicate field error
            errorResponse = handleDuplicateFieldsDB(error);
            return res.status(400).json(errorResponse);
        } else if (error.name === 'ValidationError') {
            // Mongoose validation error
            errorResponse = handleValidationErrorDB(error);
            return res.status(400).json(errorResponse);
        } else {
            // Generic error
            console.error('Signup error:', error);
            return res.status(500).json({
                status: 'error',
                message: 'Something went wrong during signup. Please try again.'
            });
        }
    }
};

exports.login = async (req, res) => {
    try {
        const { email, password } = req.body;

        // Check if email and password exist
        if (!email || !password) {
            return res.status(400).json({
                status: 'fail',
                message: 'Please provide email and password'
            });
        }

        // Check if user exists && password is correct
        const user = await User.findOne({ email }).select('+password');

        if (!user || !(await user.correctPassword(password, user.password))) {
            return res.status(401).json({
                status: 'fail',
                message: 'Incorrect email or password'
            });
        }

        createSendToken(user, 200, res);
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Something went wrong during login. Please try again.'
        });
    }
};

// User verification endpoint
exports.verifyUser = async (req, res) => {
    try {
        // The protect middleware will have already verified the token and set req.user
        const user = await User.findById(req.user._id);

        if (!user) {
            return res.status(401).json({
                status: 'fail',
                message: 'User no longer exists'
            });
        }

        res.status(200).json({
            status: 'success',
            data: {
                user
            }
        });
    } catch (error) {
        console.error('User verification error:', error);
        res.status(500).json({
            status: 'error',
            message: 'Something went wrong during user verification'
        });
    }
};