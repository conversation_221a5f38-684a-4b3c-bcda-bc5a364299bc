/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { tasks_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof tasks_v1.Tasks;
};
export declare function tasks(version: 'v1'): tasks_v1.Tasks;
export declare function tasks(options: tasks_v1.Options): tasks_v1.Tasks;
declare const auth: AuthPlus;
export { auth };
export { tasks_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
