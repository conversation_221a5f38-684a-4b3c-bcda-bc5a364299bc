/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { toolresults_v1beta3 } from './v1beta3';
export declare const VERSIONS: {
    v1beta3: typeof toolresults_v1beta3.Toolresults;
};
export declare function toolresults(version: 'v1beta3'): toolresults_v1beta3.Toolresults;
export declare function toolresults(options: toolresults_v1beta3.Options): toolresults_v1beta3.Toolresults;
declare const auth: AuthPlus;
export { auth };
export { toolresults_v1beta3 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
