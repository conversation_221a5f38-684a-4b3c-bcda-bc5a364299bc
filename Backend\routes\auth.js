const express = require('express');
const router = express.Router();
const authController = require('../controllers/authController');
const { validateSignup, validateLogin, rateLimitLogin } = require('../middleware/validation');
const { protect } = require('../middleware/auth');

// Authentication routes
router.post('/signup', validateSignup, authController.signup);
router.post('/login', validateLogin, rateLimitLogin, authController.login);

// Protected routes
router.get('/verify', protect, authController.verifyUser);

module.exports = router;