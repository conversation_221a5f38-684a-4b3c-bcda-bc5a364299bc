import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { authApi, tokenUtils, User, ApiError } from '@/services/api';
import { toast } from 'sonner';

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  signup: (name: string, email: string, password: string) => Promise<void>;
  logout: () => void;
  verifyAuth: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Verify authentication on app load
  useEffect(() => {
    verifyAuth();
  }, []);

  const verifyAuth = async () => {
    const token = tokenUtils.getToken();
    
    if (!token) {
      setIsLoading(false);
      return;
    }

    // Check if token is expired
    if (tokenUtils.isTokenExpired(token)) {
      logout();
      return;
    }

    try {
      const response = await authApi.verifyUser();
      if (response.status === 'success' && response.data?.user) {
        setUser(response.data.user);
        setIsAuthenticated(true);
      } else {
        logout();
      }
    } catch (error) {
      console.error('Auth verification failed:', error);
      logout();
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await authApi.login({ email, password });
      
      if (response.status === 'success' && response.token && response.data?.user) {
        tokenUtils.setToken(response.token);
        setUser(response.data.user);
        setIsAuthenticated(true);
        toast.success(`Welcome back, ${response.data.user.name}!`);
      } else {
        throw new Error(response.message || 'Login failed');
      }
    } catch (error) {
      if (error instanceof ApiError) {
        if (error.errors && error.errors.length > 0) {
          error.errors.forEach(err => toast.error(err));
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error('Login failed. Please try again.');
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const signup = async (name: string, email: string, password: string) => {
    try {
      setIsLoading(true);
      const response = await authApi.signup({ name, email, password });
      
      if (response.status === 'success' && response.token && response.data?.user) {
        tokenUtils.setToken(response.token);
        setUser(response.data.user);
        setIsAuthenticated(true);
        toast.success(`Welcome to Natural Write, ${response.data.user.name}!`);
      } else {
        throw new Error(response.message || 'Signup failed');
      }
    } catch (error) {
      if (error instanceof ApiError) {
        if (error.errors && error.errors.length > 0) {
          error.errors.forEach(err => toast.error(err));
        } else {
          toast.error(error.message);
        }
      } else {
        toast.error('Signup failed. Please try again.');
      }
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    tokenUtils.removeToken();
    setUser(null);
    setIsAuthenticated(false);
    setIsLoading(false);
    toast.info('You have been logged out');
  };

  const value: AuthContextType = {
    user,
    isLoading,
    isAuthenticated,
    login,
    signup,
    logout,
    verifyAuth,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
