const validator = require('validator');

// Validation middleware for signup
exports.validateSignup = (req, res, next) => {
    const { name, email, password } = req.body;
    const errors = [];

    // Validate name
    if (!name || typeof name !== 'string') {
        errors.push('Name is required and must be a string');
    } else if (name.trim().length < 2) {
        errors.push('Name must be at least 2 characters long');
    } else if (name.trim().length > 50) {
        errors.push('Name must be less than 50 characters long');
    } else if (!/^[a-zA-Z\s]+$/.test(name.trim())) {
        errors.push('Name can only contain letters and spaces');
    }

    // Validate email
    if (!email || typeof email !== 'string') {
        errors.push('Email is required and must be a string');
    } else if (!validator.isEmail(email)) {
        errors.push('Please provide a valid email address');
    } else if (email.length > 100) {
        errors.push('Email must be less than 100 characters long');
    }

    // Validate password
    if (!password || typeof password !== 'string') {
        errors.push('Password is required and must be a string');
    } else {
        if (password.length < 8) {
            errors.push('Password must be at least 8 characters long');
        }
        if (password.length > 128) {
            errors.push('Password must be less than 128 characters long');
        }
        if (!/(?=.*[a-z])/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }
        if (!/(?=.*[A-Z])/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }
        if (!/(?=.*\d)/.test(password)) {
            errors.push('Password must contain at least one number');
        }
        if (!/(?=.*[@$!%*?&])/.test(password)) {
            errors.push('Password must contain at least one special character (@$!%*?&)');
        }
    }

    if (errors.length > 0) {
        return res.status(400).json({
            status: 'fail',
            message: 'Validation failed',
            errors: errors
        });
    }

    // Sanitize inputs
    req.body.name = name.trim();
    req.body.email = email.toLowerCase().trim();
    
    next();
};

// Validation middleware for login
exports.validateLogin = (req, res, next) => {
    const { email, password } = req.body;
    const errors = [];

    // Validate email
    if (!email || typeof email !== 'string') {
        errors.push('Email is required and must be a string');
    } else if (!validator.isEmail(email)) {
        errors.push('Please provide a valid email address');
    }

    // Validate password
    if (!password || typeof password !== 'string') {
        errors.push('Password is required and must be a string');
    } else if (password.length < 1) {
        errors.push('Password is required');
    }

    if (errors.length > 0) {
        return res.status(400).json({
            status: 'fail',
            message: 'Validation failed',
            errors: errors
        });
    }

    // Sanitize inputs
    req.body.email = email.toLowerCase().trim();
    
    next();
};

// Rate limiting helper (basic implementation)
const loginAttempts = new Map();

exports.rateLimitLogin = (req, res, next) => {
    const email = req.body.email?.toLowerCase().trim();
    const now = Date.now();
    const windowMs = 15 * 60 * 1000; // 15 minutes
    const maxAttempts = 5;

    if (!email) {
        return next();
    }

    const attempts = loginAttempts.get(email) || { count: 0, resetTime: now + windowMs };

    if (now > attempts.resetTime) {
        attempts.count = 0;
        attempts.resetTime = now + windowMs;
    }

    if (attempts.count >= maxAttempts) {
        return res.status(429).json({
            status: 'fail',
            message: 'Too many login attempts. Please try again in 15 minutes.',
            retryAfter: Math.ceil((attempts.resetTime - now) / 1000)
        });
    }

    attempts.count++;
    loginAttempts.set(email, attempts);

    next();
};
