/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { travelimpactmodel_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof travelimpactmodel_v1.Travelimpactmodel;
};
export declare function travelimpactmodel(version: 'v1'): travelimpactmodel_v1.Travelimpactmodel;
export declare function travelimpactmodel(options: travelimpactmodel_v1.Options): travelimpactmodel_v1.Travelimpactmodel;
declare const auth: AuthPlus;
export { auth };
export { travelimpactmodel_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
