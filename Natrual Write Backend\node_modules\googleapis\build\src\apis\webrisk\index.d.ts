/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { webrisk_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof webrisk_v1.Webrisk;
};
export declare function webrisk(version: 'v1'): webrisk_v1.Webrisk;
export declare function webrisk(options: webrisk_v1.Options): webrisk_v1.Webrisk;
declare const auth: AuthPlus;
export { auth };
export { webrisk_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
