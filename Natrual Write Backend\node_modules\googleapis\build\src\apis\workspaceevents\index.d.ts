/*! THIS FILE IS AUTO-GENERATED */
import { AuthPlus } from 'googleapis-common';
import { workspaceevents_v1 } from './v1';
export declare const VERSIONS: {
    v1: typeof workspaceevents_v1.Workspaceevents;
};
export declare function workspaceevents(version: 'v1'): workspaceevents_v1.Workspaceevents;
export declare function workspaceevents(options: workspaceevents_v1.Options): workspaceevents_v1.Workspaceevents;
declare const auth: AuthPlus;
export { auth };
export { workspaceevents_v1 };
export { AuthPlus, GlobalOptions, APIRequestContext, GoogleConfigurable, StreamMethodOptions, MethodOptions, BodyResponseCallback, } from 'googleapis-common';
