import React from 'react';
import { Button } from '@/components/ui/button';
import { FileText, History, User, LogOut } from 'lucide-react';
import { useNavigate, Link } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

const Header = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user, logout } = useAuth();

  const handleLogout = () => {
    logout();
    navigate('/');
  };

  return (
    <header className="bg-white border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center space-x-2.5">
            <Link to="/" className="flex items-center space-x-2.5">
              <div className="w-9 h-9 bg-[#4f46e5] rounded-xl flex items-center justify-center shadow-sm">
                <FileText className="w-5 h-5 text-white" />
              </div>
              <span className="text-xl font-semibold text-gray-900">Natural Write</span>
            </Link>
          </div>

          <nav className="hidden md:flex items-center space-x-8">
            <Link to="/pricing" className="text-gray-600 hover:text-gray-900 transition-colors">Pricing</Link>
            <Link to="#" className="text-gray-600 hover:text-gray-900 transition-colors">How it Works</Link>
            <Link to="#" className="text-gray-600 hover:text-gray-900 transition-colors">Blog</Link>
            <Link to="mailto:<EMAIL>" className="text-gray-600 hover:text-gray-900 transition-colors">Contact</Link>
          </nav>

          <div className="flex items-center space-x-3">
            {isAuthenticated ? (
              <>
                <span className="text-sm text-gray-600 hidden sm:block">
                  Welcome, {user?.name}
                </span>
                <Button
                  onClick={() => navigate('/pricing')}
                  className="bg-[#4f46e5] hover:bg-[#4338ca] text-white px-4 py-2 rounded-lg text-sm font-medium"
                >
                  Get more words
                </Button>
                <Button variant="ghost" size="icon" className="w-9 h-9 rounded-lg">
                  <History className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-9 h-9 rounded-lg"
                  onClick={() => navigate('/profile')}
                >
                  <User className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="w-9 h-9 rounded-lg text-red-600 hover:text-red-700 hover:bg-red-50"
                  onClick={handleLogout}
                  title="Logout"
                >
                  <LogOut className="w-5 h-5" />
                </Button>
              </>
            ) : (
              <>
                <Button
                  variant="ghost"
                  onClick={() => navigate('/signin')}
                  className="text-gray-600 hover:text-gray-900"
                >
                  Log in
                </Button>
                <Button
                  onClick={() => navigate('/signup')}
                  className="bg-[#4f46e5] hover:bg-[#4338ca] text-white px-4 py-2 rounded-lg text-sm font-medium"
                >
                  Try for free
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
